# Environment Configuration
NODE_ENV=development

# OpenSearch Configuration
OS_HOST=https://search-healthtechgate-provider-o2nranqpg5ehoncqitfnu7fz2a.us-east-1.es.amazonaws.com
REGION=us-east-1

# Server Configuration
SERVER_URL=http://localhost:3000
PORT=3000
BASE_PATH=
X_API_KEY=sqi9oXyvQa4btGV24j1Jw9MSkHpcOvCo9BBY7eEZ

# Fitbit Configuration
FITBIT_AUTHORIZE_URL=https://www.fitbit.com/oauth2/authorize
FITBIT_API_BASE_URL=https://api.fitbit.com
FITBIT_CLIENT_ID=your-client-id
FITBIT_CLIENT_SECRET=your-client-secret
FITBIT_SUBSCRIBER_ID=1
FITBIT_VERIFICATION_CODE=your-verification-code

# Dexcom Configuration
DEXCOM_AUTHORIZE_URL=https://sandbox-api.dexcom.com/v2/oauth2/login
DEXCOM_API_BASE_URL=https://sandbox-api.dexcom.com
DEXCOM_CLIENT_ID=your-client-id
DEXCOM_CLIENT_SECRET=your-client-secret
DEXCOM_AUTHORIZE_REDIRECT=http://localhost:3000/trackers/dexcom/callback

# Oura Configuration
OURA_AUTHORIZE_URL=https://cloud.ouraring.com/oauth/authorize
OURA_API_BASE_URL=https://api.ouraring.com
OURA_CLIENT_ID=your-client-id
OURA_CLIENT_SECRET=your-client-secret
OURA_AUTHORIZE_REDIRECT=http://localhost:3000/trackers/oura/callback

# AWS Configuration
AWS_SQS_URL=https://sqs.us-east-1.amazonaws.com/your-account-id/your-queue-name.fifo
AWS_USER_CONNECTIONS_LAMBDA=user-connections-dev
AWS_PUSH_NOTIFICATIONS_LAMBDA=push-notifications-dev
AWS_TRACKERS_STATIC_DATA_LAMBDA=trackers-static-dev
AWS_TARGET_COMPUTATION_SQS_URL=https://sqs.us-east-1.amazonaws.com/your-account-id/target-achievements-dev.fifo
AWS_TRACKERS_INGESTION_SQS_URL=https://sqs.us-east-1.amazonaws.com/your-account-id/trackers-ingestion-dev.fifo
AWS_TRACKERS_INGESTION_LOGS_S3_BUCKET=your-bucket-name-dev

# Monolith Configuration
MONOLITH_SERVER_URL=https://api.dev.your-domain.com

# Dynalinks Configuration
DYNALINKS_BASE_URL=https://your-app.dynalinks.app
DYNALINKS_API_KEY=your-api-key
DYNALINKS_FORMS=https://your-app.dynalinks.app/forms
DYNALINKS_WELLNESS_SCORE=https://your-app.dynalinks.app/wellness-score
DYNALINKS_DEVICE_CONNECTION=https://your-app.dynalinks.app/devices
DYNALINKS_DEVICE_PERMISSION=https://your-app.dynalinks.app/permissions

# Marketing Website
MARKETING_WEBSITE=https://www.your-domain.com
