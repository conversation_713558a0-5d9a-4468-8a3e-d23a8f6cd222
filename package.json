{"name": "trackers", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "start:dev": "NODE_ENV=development nodemon index.js", "start:sandbox": "NODE_ENV=sandbox nodemon index.js", "start:prod": "NODE_ENV=production node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-opensearch": "^3.190.0", "@opensearch-project/opensearch": "^2.0.0", "ajv": "^8.11.0", "ajv-formats": "^2.1.1", "aws-sdk": "^2.1249.0", "aws4": "^1.11.0", "axios": "1.1.0", "body-parser": "^1.20.1", "bunyan": "^1.8.15", "bunyan-format": "^0.2.1", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.20", "nconf": "^0.10.0", "nodemon": "^2.0.20", "serverless-http": "^3.1.0", "uuid": "^9.0.0"}}