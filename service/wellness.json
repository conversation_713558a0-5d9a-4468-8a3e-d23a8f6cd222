{"score": null, "style": {"light": {"primaryButton": {"background": {"hex": "#2096F3"}, "text": {"hex": "#FFFFFF"}}, "card": {"background": {"gradient": {"start": "#FFCDCB", "middle": "#FFFFFF", "end": "#FFFFFF", "degree": "127", "type": "linear"}, "border": "1px solid #F8B2B2", "opacity": 0.4}}}, "dark": {"primaryButton": {"background": {"hex": "#BB86FC"}, "text": {"hex": "#000000"}}, "card": {"background": {"gradient": {"start": "#6200EE", "middle": "#121212", "end": "#121212", "degree": "127", "type": "linear"}, "border": "1px solid #B388FF", "opacity": 0.4}}}}, "content": {"title": "Your Wellness Score", "subtitle": "Your wellness score indicates your overall health and well-being. It's based on various factors such as physical activity, nutrition, sleep quality, and stress levels.", "media": {"src": "remote", "imageUrl": null, "type": null, "mediaOverlayText": "0"}, "primaryAction": {"title": "Learn more", "url": "https://20deg.dynalinks.app/wellness-score"}}, "details": [{"id": 1, "label": "Data Capture", "score": null, "targetId": null, "description": "Authorized trackers with user consent", "details": []}, {"id": 2, "label": "Adherence", "score": null, "targetId": null, "description": "Tracking default targets achieved in the previous week", "details": [{"id": 3, "label": "Consistency", "score": null, "targetId": null, "description": "Meeting weekly targets"}, {"id": 4, "label": "Food capture via platform", "score": null, "targetId": 15, "description": "Meal entry using platform"}, {"id": 5, "label": "Exercise capture via platform", "score": null, "targetId": 10, "description": "Exercise entry using platform"}, {"id": 6, "label": "Stress capture via platform", "score": null, "targetId": 11, "description": "Mindfulness entry using platform"}, {"id": 7, "label": "Extra Targets/Goals", "score": null, "targetId": null, "description": "Meeting additional targets beyond defaults"}]}, {"id": 8, "label": "Progress on goals", "score": null, "targetId": null, "description": "Advancing goals weekly", "details": []}]}